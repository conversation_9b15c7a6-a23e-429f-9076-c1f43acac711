<script setup>
import AppLayout from '@/layouts/AppLayout.vue';
import VideoCallModal from '@/components/VideoCallModal.vue';
import { Head, Link, router, usePage } from '@inertiajs/vue3';
import { ref, onMounted, computed } from 'vue';

// Get current user information
const page = usePage();
const currentUser = computed(() => page.props.auth?.user);

// Dynamic breadcrumbs based on user role
const breadcrumbs = computed(() => {
    const userRole = currentUser.value?.role;
    let appointmentsHref = '/appointments';

    if (userRole === 'provider') {
        appointmentsHref = '/provider/appointments';
    } else if (userRole === 'patient') {
        appointmentsHref = '/patient/appointments';
    } else if (userRole === 'admin' || userRole === 'manager' || userRole === 'super_admin') {
        appointmentsHref = '/manage/appointments';
    }

    return [
        { title: 'Dashboard', href: '/dashboard' },
        { title: 'Appointments', href: appointmentsHref },
    ];
});

const loading = ref(false);
const appointments = ref([]);
const allAppointments = ref([]);
const actionLoading = ref({});
const dateFilter = ref('upcoming'); // 'upcoming', 'past', 'all'


const isPatient = computed(() => currentUser.value?.role === 'patient');
const isProvider = computed(() => currentUser.value?.role === 'provider');

// Video call state
const showVideoCall = ref(false);
const selectedAppointment = ref(null);
const userRole = ref('patient'); // Will be determined based on user

// Reason popup state
const showReasonPopup = ref(false);
const selectedReason = ref('');

// Join consult state
const waitingForProvider = ref({});
const joinConsultLoading = ref({});

// Helper function to determine user's role for a specific appointment
const getUserRoleForAppointment = (appointment) => {
    if (!currentUser.value) return null;

    // Check if current user is the provider for this appointment
    if (currentUser.value.role === 'provider' &&
        appointment.provider?.user?.id === currentUser.value.id) {
        return 'provider';
    }

    // Check if current user is the patient for this appointment
    if (currentUser.value.role === 'patient' &&
        appointment.patient?.user?.id === currentUser.value.id) {
        return 'patient';
    }

    // Admin/manager can act as provider
    if (['admin', 'manager'].includes(currentUser.value.role)) {
        return 'provider';
    }

    return null;
};

// Helper function to check if user can start a video call (providers only)
const canStartVideoCall = (appointment) => {
    const role = getUserRoleForAppointment(appointment);
    return role === 'provider' &&
           appointment.is_telemedicine &&
           ['scheduled', 'confirmed', 'in_progress'].includes(appointment.status);
};

// Helper function to check if user can join a video call (patients only)
const canJoinVideoCall = (appointment) => {
    const role = getUserRoleForAppointment(appointment);
    return role === 'patient' &&
           appointment.is_telemedicine &&
           ['scheduled', 'confirmed', 'in_progress'].includes(appointment.status) &&
           appointment.video_session_id; // Session must exist
};

const fetchAppointments = async () => {
    loading.value = true;
    try {
        // Get CSRF token from meta tag
        const tokenMeta = document.querySelector('meta[name="csrf-token"]');
        const token = tokenMeta ? tokenMeta.getAttribute('content') : '';

        const response = await fetch('/appointments-list', {
            method: 'GET',
            headers: {
                'Accept': 'application/json',
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest',
                'X-CSRF-TOKEN': token || '',
            },
            credentials: 'same-origin'
        });

        if (response.ok) {
            const data = await response.json();
            // The API returns appointments directly as an array
            allAppointments.value = Array.isArray(data) ? data : (data.appointments || data.data || []);
            filterAppointments();
        } else if (response.status === 401) {
            // User not authenticated, redirect to login
            router.visit('/login');
            return;
        } else {
            console.error('Failed to fetch appointments:', response.status, await response.text());
            appointments.value = [];
        }
    } catch (error) {
        console.error('Error fetching appointments:', error);
        appointments.value = [];
    } finally {
        loading.value = false;
    }
};

// Filter appointments based on date filter
const filterAppointments = () => {
    // Safety check: ensure allAppointments.value is an array
    if (!Array.isArray(allAppointments.value)) {
        console.warn('allAppointments.value is not an array in filterAppointments:', allAppointments.value);
        allAppointments.value = [];
        appointments.value = [];
        return;
    }

    const now = new Date();
    now.setHours(0, 0, 0, 0); // Start of today

    if (dateFilter.value === 'upcoming') {
        appointments.value = allAppointments.value.filter(appointment => {
            const appointmentDate = new Date(appointment.date || appointment.scheduled_at);
            appointmentDate.setHours(0, 0, 0, 0);
            return appointmentDate >= now;
        });
    } else if (dateFilter.value === 'past') {
        appointments.value = allAppointments.value.filter(appointment => {
            const appointmentDate = new Date(appointment.date || appointment.scheduled_at);
            appointmentDate.setHours(0, 0, 0, 0);
            return appointmentDate < now;
        });
    } else {
        appointments.value = [...allAppointments.value];
    }
};

// Change date filter
const changeDateFilter = (filter) => {
    dateFilter.value = filter;
    filterAppointments();
};

// Computed properties for stats
const totalAppointments = computed(() => {
    return Array.isArray(allAppointments.value) ? allAppointments.value.length : 0;
});
const upcomingAppointments = computed(() => {
    if (!Array.isArray(allAppointments.value)) return 0;

    const now = new Date();
    now.setHours(0, 0, 0, 0);
    return allAppointments.value.filter(appointment => {
        const appointmentDate = new Date(appointment.date || appointment.scheduled_at);
        appointmentDate.setHours(0, 0, 0, 0);
        return appointmentDate >= now;
    }).length;
});
const completedAppointments = computed(() => {
    if (!Array.isArray(allAppointments.value)) return 0;
    return allAppointments.value.filter(appointment => appointment.status === 'completed').length;
});

const getStatusBadgeClass = (status) => {
    const classes = {
        scheduled: 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300',
        completed: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300',
        cancelled: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300',
        no_show: 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300'
    };
    return classes[status] || 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300';
};

const getTypeBadgeClass = (type) => {
    const classes = {
        telemedicine: 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300',
        'in-person': 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-300'
    };
    return classes[type] || 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300';
};

const getPaymentStatusBadgeClass = (status) => {
    const classes = {
        paid: 'bg-green-100 text-green-800 border border-green-200',
        pending: 'bg-yellow-100 text-yellow-800 border border-yellow-200',
        unpaid: 'bg-red-100 text-red-800 border border-red-200',
        failed: 'bg-red-100 text-red-800 border border-red-200',
        refunded: 'bg-gray-100 text-gray-800 border border-gray-200',
        pending_payment: 'bg-orange-100 text-orange-800 border border-orange-200'
    };
    return classes[status] || 'bg-gray-100 text-gray-800 border border-gray-200';
};

const getPaymentStatusText = (status) => {
    const texts = {
        paid: '✓ Paid',
        pending: '⏳ Pending',
        unpaid: '❌ Unpaid',
        failed: '❌ Failed',
        refunded: '↩️ Refunded',
        pending_payment: '💳 Payment Required'
    };
    return texts[status] || 'Unknown';
};

// Action methods
const viewAppointment = (appointment) => {
    router.visit(`/appointments/${appointment.id}`);
};

const editAppointment = (appointment) => {
    // Navigate to edit page or open edit modal
    router.visit(`/appointments/${appointment.id}/edit`);
};

const rescheduleAppointment = (appointment) => {
    // Navigate to reschedule page or open reschedule modal
    router.visit(`/appointments/${appointment.id}/reschedule`);
};

const deleteAppointment = async (appointment) => {
    const appointmentInfo = `${appointment.service?.name || 'Consultation'} on ${formatDate(appointment.date || appointment.scheduled_at)} at ${formatTime(appointment)}`;

    if (!confirm(`Are you sure you want to permanently delete this appointment?\n\n${appointmentInfo}\n\nThis action cannot be undone.`)) {
        return;
    }

    actionLoading.value[appointment.id] = true;

    try {
        const response = await axios.delete(`/delete-appointment/${appointment.id}`);

        if (response.status === 200) {
            // Remove the appointment from the local array with safety checks
            if (Array.isArray(appointments.value)) {
                appointments.value = appointments.value.filter(a => a.id !== appointment.id);
            }
            if (Array.isArray(allAppointments.value)) {
                allAppointments.value = allAppointments.value.filter(a => a.id !== appointment.id);
            }

            // Show success message
            alert('Appointment deleted successfully.');
        } else {
            const errorData = await response.json().catch(() => ({}));
            alert(errorData.message || 'Failed to delete appointment. Please try again.');
        }
    } catch (error) {
        console.error('Error deleting appointment:', error);
        alert('Failed to delete appointment. Please try again.');
    } finally {
        actionLoading.value[appointment.id] = false;
    }
};

const payForAppointment = (appointment) => {
    // Navigate to payment page
    router.visit(`/appointments/${appointment.id}/payment`);
};

// Format and clean reason text
const formatReason = (reason) => {
    if (!reason) return 'No reason provided'

    // Clean up markdown and formatting
    const cleaned = reason
        .replace(/\*\*/g, '') // Remove bold markdown
        .replace(/###/g, '') // Remove headers
        .replace(/\n+/g, ' ') // Replace line breaks with spaces
        .trim()

    return cleaned
}

const showReason = (reason) => {
    selectedReason.value = reason;
    showReasonPopup.value = true;
};

const closeReasonPopup = () => {
    showReasonPopup.value = false;
    selectedReason.value = '';
};

const cancelAppointment = async (appointment) => {
    if (!confirm(`Are you sure you want to cancel the appointment with ${appointment.provider_name || appointment.provider?.name || 'the provider'}?`)) {
        return;
    }

    actionLoading.value[appointment.id] = true;

    try {
        // Get CSRF token from meta tag
        const tokenMeta = document.querySelector('meta[name="csrf-token"]');
        const token = tokenMeta ? tokenMeta.getAttribute('content') : '';
        
        if (!token) {
            console.error('CSRF token not found in meta tag');
            alert('Security token not found. Please refresh the page and try again.');
            return;
        }

        const response = await fetch(`/delete-appointment/${appointment.id}`, {
            method: 'DELETE',
            headers: {
                'Accept': 'application/json',
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest',
                'X-CSRF-TOKEN': token,
            },
            credentials: 'same-origin'
        });

        if (response.ok) {
            // Update the appointment status locally with safety check
            if (Array.isArray(appointments.value)) {
                const appointmentIndex = appointments.value.findIndex(a => a.id === appointment.id);
                if (appointmentIndex !== -1) {
                    appointments.value[appointmentIndex].status = 'cancelled';
                }
            }

            // Show success message
            alert('Appointment cancelled successfully.');
        } else {
            const errorData = await response.json().catch(() => ({}));
            alert(errorData.message || 'Failed to cancel appointment. Please try again.');
        }
    } catch (error) {
        console.error('Error cancelling appointment:', error);
        alert('Failed to cancel appointment. Please try again.');
    } finally {
        actionLoading.value[appointment.id] = false;
    }
};

const isActionLoading = (appointmentId) => {
    return actionLoading.value[appointmentId] || false;
};

// Video call functions
const startVideoCall = async (appointment) => {
    console.log('Attempting to start video call for appointment:', appointment.id);

    // Check user's role for this appointment
    const role = getUserRoleForAppointment(appointment);

    if (!role) {
        alert('You are not authorized to start this video call.');
        return;
    }

    // Only providers can start video calls
    if (role !== 'provider') {
        alert('Only the provider can start the video call. Please wait for the provider to begin the session.');
        return;
    }

    try {
        // Get CSRF token more reliably
        const tokenMeta = document.querySelector('meta[name="csrf-token"]');
        const token = tokenMeta ? tokenMeta.getAttribute('content') : '';
        
        if (!token) {
            console.error('CSRF token not found in meta tag');
            alert('Security token not found. Please refresh the page and try again.');
            return;
        }

        console.log('Using CSRF token:', token.substring(0, 10) + '...');
        
        // Initialize video session
        const response = await fetch(`/video/initialize/${appointment.id}`, {
            method: 'POST',
            headers: {
                'Accept': 'application/json',
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest',
                'X-CSRF-TOKEN': token,
            },
            credentials: 'same-origin'
        });

        if (response.status === 419) {
            console.error('CSRF token mismatch - refreshing page');
            alert('Security token expired. The page will refresh automatically.');
            window.location.reload();
            return;
        }

        const responseData = await response.json().catch(() => ({}));

        if (response.ok && responseData.success) {
            console.log('Video session initialized successfully');

            // Update appointment with session data if provided
            if (responseData.session_data) {
                appointment.video_session_id = responseData.session_id;
                appointment.session_data = responseData.session_data;
                appointment.user_role = responseData.user_role;
            }

            selectedAppointment.value = appointment;
            userRole.value = role; // Use determined role
            showVideoCall.value = true;
        } else {
            const errorMessage = responseData.message || 'Failed to start video call. Please try again.';
            console.error('Failed to initialize video session:', errorMessage);
            alert(errorMessage);
        }
    } catch (error) {
        console.error('Error starting video call:', error);
        alert('Network error occurred. Please check your connection and try again.');
    }
};

const joinConsult = async (appointment) => {
    console.log('Attempting to join consult for appointment:', appointment.id);

    // Check user's role for this appointment
    const role = getUserRoleForAppointment(appointment);

    if (!role) {
        alert('You are not authorized to join this consultation.');
        return;
    }

    // If user is a provider, they should start the call instead
    if (role === 'provider') {
        alert('As the provider, please use "Start Call" to begin the video session.');
        return;
    }

    // Set loading state
    joinConsultLoading.value[appointment.id] = true;

    try {
        // Always open the video consultation screen - even if provider hasn't started yet
        selectedAppointment.value = appointment;
        userRole.value = role;
        showVideoCall.value = true;
        joinConsultLoading.value[appointment.id] = false;

        // If no video session exists yet, start checking for provider
        if (!appointment.video_session_id) {
            waitingForProvider.value[appointment.id] = true;
            startProviderCheck(appointment);
        }
        
    } catch (error) {
        console.error('Error joining consult:', error);
        alert('Failed to join consultation. Please try again.');
        joinConsultLoading.value[appointment.id] = false;
    }
};

const startProviderCheck = (appointment) => {
    const checkInterval = setInterval(async () => {
        try {
            // Only check if we're still waiting and the video modal is open
            if (!waitingForProvider.value[appointment.id] || !showVideoCall.value) {
                clearInterval(checkInterval);
                return;
            }

            // Fetch updated appointment data
            const tokenMeta = document.querySelector('meta[name="csrf-token"]');
            const token = tokenMeta ? tokenMeta.getAttribute('content') : '';
            
            const response = await fetch(`/appointments/${appointment.id}`, {
                method: 'GET',
                headers: {
                    'Accept': 'application/json',
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest',
                    'X-CSRF-TOKEN': token || '',
                },
                credentials: 'same-origin'
            });

            if (response.ok) {
                const updatedAppointment = await response.json();
                
                // Check if provider has started the session
                if (updatedAppointment.video_session_id) {
                    clearInterval(checkInterval);
                    waitingForProvider.value[appointment.id] = false;
                    
                    // Update the appointment object with safety check
                    if (Array.isArray(appointments.value)) {
                        const appointmentIndex = appointments.value.findIndex(a => a.id === appointment.id);
                        if (appointmentIndex !== -1) {
                            appointments.value[appointmentIndex].video_session_id = updatedAppointment.video_session_id;
                            appointments.value[appointmentIndex].session_data = updatedAppointment.session_data;
                        }
                    }
                    
                    // Update the selected appointment for the video call
                    selectedAppointment.value = updatedAppointment;
                    
                    // The VideoCallModal will automatically initialize when it detects the session
                    console.log('Provider has started the session, video call will initialize automatically');
                }
            }
        } catch (error) {
            console.error('Error checking provider status:', error);
        }
    }, 3000); // Check every 3 seconds

    // Stop checking after 5 minutes
    setTimeout(() => {
        clearInterval(checkInterval);
        waitingForProvider.value[appointment.id] = false;
    }, 300000);
};

const joinVideoCall = (appointment) => {
    console.log('Attempting to join video call for appointment:', appointment.id);

    // Check user's role for this appointment
    const role = getUserRoleForAppointment(appointment);

    if (!role) {
        alert('You are not authorized to join this video call.');
        return;
    }

    // If user is a provider, they should start the call instead
    if (role === 'provider') {
        alert('As the provider, please use "Start Call" to begin the video session.');
        return;
    }

    // Validate appointment has video session
    if (!appointment.video_session_id) {
        console.error('No video session found for appointment');
        alert('Video session not available yet. Please wait for the provider to start the call first.');
        return;
    }

    selectedAppointment.value = appointment;
    userRole.value = role; // Use determined role
    showVideoCall.value = true;
};

const closeVideoCall = () => {
    console.log('Closing video call');
    showVideoCall.value = false;
    selectedAppointment.value = null;
    // Refresh appointments to get updated status
    fetchAppointments();
};

const handleVideoCallError = (error) => {
    console.error('Video call error:', error);

    // Provide more specific error messages
    let errorMessage = 'Video call error occurred';
    if (typeof error === 'string') {
        errorMessage = error;
    } else if (error && error.message) {
        errorMessage = error.message;
    }

    // Check for common error types
    if (errorMessage.includes('INVALID_REMOTE_USER')) {
        errorMessage = 'Unable to connect to the other participant. Please try again.';
    } else if (errorMessage.includes('NETWORK_ERROR')) {
        errorMessage = 'Network connection issue. Please check your internet connection.';
    } else if (errorMessage.includes('permission')) {
        errorMessage = 'Camera or microphone permission denied. Please allow access and try again.';
    }

    alert(errorMessage);
    closeVideoCall();
};

// Date and time formatting functions
const formatDate = (dateString) => {
    if (!dateString) return 'N/A';

    try {
        const date = new Date(dateString);
        const today = new Date();
        const tomorrow = new Date(today);
        tomorrow.setDate(tomorrow.getDate() + 1);

        // Check if it's today
        if (date.toDateString() === today.toDateString()) {
            return 'Today';
        }

        // Check if it's tomorrow
        if (date.toDateString() === tomorrow.toDateString()) {
            return 'Tomorrow';
        }

        // Format as readable date
        return date.toLocaleDateString('en-US', {
            weekday: 'short',
            month: 'short',
            day: 'numeric',
            year: date.getFullYear() !== today.getFullYear() ? 'numeric' : undefined
        });
    } catch (error) {
        return dateString;
    }
};

const formatTime = (appointment) => {
    // Try to get time from time_slot first
    if (appointment.time_slot?.start_time) {
        const startTime = appointment.time_slot.start_time;
        const endTime = appointment.time_slot.end_time;

        try {
            // Convert 24-hour format to 12-hour format
            const formatTime12Hour = (time24) => {
                const [hours, minutes] = time24.split(':');
                const hour = parseInt(hours);
                const ampm = hour >= 12 ? 'PM' : 'AM';
                const hour12 = hour % 12 || 12;
                return `${hour12}:${minutes} ${ampm}`;
            };

            if (endTime) {
                return `${formatTime12Hour(startTime)} - ${formatTime12Hour(endTime)}`;
            } else {
                return formatTime12Hour(startTime);
            }
        } catch (error) {
            return `${startTime}${endTime ? ` - ${endTime}` : ''}`;
        }
    }

    // Fallback to other time fields
    if (appointment.time) {
        return appointment.time;
    }

    if (appointment.scheduled_at) {
        try {
            const date = new Date(appointment.scheduled_at);
            return date.toLocaleTimeString('en-US', {
                hour: 'numeric',
                minute: '2-digit',
                hour12: true
            });
        } catch (error) {
            return appointment.scheduled_at.split('T')[1]?.substring(0, 5) || 'N/A';
        }
    }

    return 'N/A';
};

const formatPaymentDate = (dateString) => {
    if (!dateString) return '';

    try {
        const date = new Date(dateString);
        return date.toLocaleDateString('en-US', {
            month: 'short',
            day: 'numeric'
        });
    } catch (error) {
        return '';
    }
};

onMounted(() => {
    fetchAppointments();
});
</script>

<template>
    <Head title="Appointment Management" />

    <AppLayout>
        <template #header>
            <div class="flex items-center justify-between">
                <div>
                    <h2 class="text-xl font-semibold leading-tight text-gray-800 dark:text-gray-200">
                        Appointment Management
                    </h2>
                    <nav class="flex mt-2" aria-label="Breadcrumb">
                        <ol class="inline-flex items-center space-x-1 md:space-x-3">
                            <li v-for="(breadcrumb, index) in breadcrumbs" :key="index" class="inline-flex items-center">
                                <Link v-if="index < breadcrumbs.length - 1" 
                                    :href="breadcrumb.href" 
                                    class="text-sm font-medium text-gray-500 hover:text-blue-600 dark:text-gray-400 dark:hover:text-white">
                                    {{ breadcrumb.title }}
                                </Link>
                                <span v-else class="text-sm font-medium text-gray-700 dark:text-gray-400">
                                    {{ breadcrumb.title }}
                                </span>
                                <svg v-if="index < breadcrumbs.length - 1" class="w-3 h-3 mx-1 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                                </svg>
                            </li>
                        </ol>
                    </nav>
                </div>
            </div>
        </template>

        <div class="py-6 h-full overflow-hidden">
            <div class="mx-auto max-w-7xl sm:px-4 lg:px-6 h-full flex flex-col">
                <!-- Compact Stats Cards -->
                <div v-if="isPatient" class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                    <div class="bg-white rounded-xl shadow-sm border border-blue-100 overflow-hidden hover:shadow-lg hover:border-blue-200 transition-all duration-300 group">
                        <div class="p-6">
                            <div class="flex items-center">
                                <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center shadow-sm group-hover:shadow-md transition-shadow">
                                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                                    </svg>
                                </div>
                                <div class="ml-4">
                                    <p class="text-sm font-medium text-gray-600">Total Appointments</p>
                                    <p class="text-2xl font-bold text-gray-900">{{ totalAppointments }}</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="bg-white rounded-xl shadow-sm border border-orange-100 overflow-hidden hover:shadow-lg hover:border-orange-200 transition-all duration-300 group">
                        <div class="p-6">
                            <div class="flex items-center">
                                <div class="w-12 h-12 bg-gradient-to-br from-orange-500 to-amber-500 rounded-xl flex items-center justify-center shadow-sm group-hover:shadow-md transition-shadow">
                                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                                    </svg>
                                </div>
                                <div class="ml-4">
                                    <p class="text-sm font-medium text-gray-600">Upcoming</p>
                                    <p class="text-2xl font-bold text-gray-900">{{ upcomingAppointments }}</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="bg-white rounded-xl shadow-sm border border-green-100 overflow-hidden hover:shadow-lg hover:border-green-200 transition-all duration-300 group">
                        <div class="p-6">
                            <div class="flex items-center">
                                <div class="w-12 h-12 bg-gradient-to-br from-green-500 to-emerald-600 rounded-xl flex items-center justify-center shadow-sm group-hover:shadow-md transition-shadow">
                                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                    </svg>
                                </div>
                                <div class="ml-4">
                                    <p class="text-sm font-medium text-gray-600">Completed</p>
                                    <p class="text-2xl font-bold text-gray-900">{{ completedAppointments }}</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Date Filter Buttons -->
                <div class="flex items-center justify-between mb-6">
                    <div class="flex space-x-1 bg-gray-100 p-1 rounded-xl">
                        <button
                            @click="changeDateFilter('upcoming')"
                            :class="[
                                'px-6 py-2.5 text-sm font-medium rounded-lg transition-all duration-200',
                                dateFilter === 'upcoming'
                                    ? 'bg-white text-blue-600 shadow-sm border border-blue-200'
                                    : 'text-gray-600 hover:text-blue-600 hover:bg-white/50'
                            ]"
                        >
                            Upcoming
                        </button>
                        <button
                            @click="changeDateFilter('past')"
                            :class="[
                                'px-6 py-2.5 text-sm font-medium rounded-lg transition-all duration-200',
                                dateFilter === 'past'
                                    ? 'bg-white text-blue-600 shadow-sm border border-blue-200'
                                    : 'text-gray-600 hover:text-blue-600 hover:bg-white/50'
                            ]"
                        >
                            Past
                        </button>
                        <button
                            @click="changeDateFilter('all')"
                            :class="[
                                'px-6 py-2.5 text-sm font-medium rounded-lg transition-all duration-200',
                                dateFilter === 'all'
                                    ? 'bg-white text-blue-600 shadow-sm border border-blue-200'
                                    : 'text-gray-600 hover:text-blue-600 hover:bg-white/50'
                            ]"
                        >
                            All
                        </button>
                    </div>
                    <div class="text-sm text-gray-500 bg-white px-4 py-2 rounded-lg border border-gray-200">
                        Showing {{ appointments.length }} of {{ totalAppointments }} appointments
                    </div>
                </div>

                <!-- Provider Stats -->
                <div v-if="!isPatient" class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
                    <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
                        <div class="p-6">
                            <div class="flex items-center">
                                <div class="flex-shrink-0">
                                    <i class="fas fa-calendar-alt text-2xl text-blue-500"></i>
                                </div>
                                <div class="ml-4">
                                    <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Total Appointments</p>
                                    <p class="text-2xl font-semibold text-gray-900 dark:text-gray-100">{{ appointments.length }}</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
                        <div class="p-6">
                            <div class="flex items-center">
                                <div class="flex-shrink-0">
                                    <i class="fas fa-clock text-2xl text-yellow-500"></i>
                                </div>
                                <div class="ml-4">
                                    <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Scheduled</p>
                                    <p class="text-2xl font-semibold text-gray-900 dark:text-gray-100">
                                        {{ Array.isArray(appointments) ? appointments.filter(a => a.status === 'scheduled').length : 0 }}
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
                        <div class="p-6">
                            <div class="flex items-center">
                                <div class="flex-shrink-0">
                                    <i class="fas fa-check-circle text-2xl text-green-500"></i>
                                </div>
                                <div class="ml-4">
                                    <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Completed</p>
                                    <p class="text-2xl font-semibold text-gray-900 dark:text-gray-100">
                                        {{ Array.isArray(appointments) ? appointments.filter(a => a.status === 'completed').length : 0 }}
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
                        <div class="p-6">
                            <div class="flex items-center">
                                <div class="flex-shrink-0">
                                    <i class="fas fa-dollar-sign text-2xl text-green-600"></i>
                                </div>
                                <div class="ml-4">
                                    <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Total Revenue</p>
                                    <p class="text-2xl font-semibold text-gray-900 dark:text-gray-100">
                                        ${{ Array.isArray(appointments) ? appointments.reduce((sum, a) => sum + (parseFloat(a.amount || a.service?.price || a.price) || 0), 0).toFixed(2) : '0.00' }}
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Provider Appointments Table -->
                <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg flex-1 flex flex-col min-h-0">
                    <div class="p-6 text-gray-900 dark:text-gray-100 flex-1 flex flex-col min-h-0">
                        <div v-if="loading" class="text-center py-8">
                            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
                        </div>

                        <div v-else class="flex-1 overflow-auto">
                            <div class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                                <thead class="bg-gray-50 dark:bg-gray-700">
                                    <tr>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                            Appointment
                                        </th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                            Patient
                                        </th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                            Provider
                                        </th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                            Date & Time
                                        </th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                            Status
                                        </th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                            Amount
                                        </th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                            Actions
                                        </th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                                    <tr v-for="appointment in appointments" :key="appointment.id" class="hover:bg-gray-50 dark:hover:bg-gray-700">
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm font-medium text-gray-900 dark:text-gray-100">
                                                {{ appointment.service?.name || appointment.reason || 'Consultation' }}
                                            </div>
                                            <div class="text-sm text-gray-500 dark:text-gray-400">
                                                <span :class="getTypeBadgeClass(appointment.is_telemedicine ? 'telemedicine' : 'in-person')" class="inline-flex px-2 py-1 text-xs font-semibold rounded-full">
                                                    {{ appointment.is_telemedicine ? 'telemedicine' : 'in-person' }}
                                                </span>
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm text-gray-900 dark:text-gray-100">{{ appointment.patient_name || appointment.patient?.name || 'N/A' }}</div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm text-gray-900 dark:text-gray-100">{{ appointment.provider_name || appointment.provider?.name || 'N/A' }}</div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm text-gray-900 dark:text-gray-100">{{ formatDate(appointment.date || appointment.scheduled_at) }}</div>
                                            <div class="text-sm text-gray-500 dark:text-gray-400">{{ formatTime(appointment) }}</div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span :class="getStatusBadgeClass(appointment.status)" class="inline-flex px-2 py-1 text-xs font-semibold rounded-full">
                                                {{ appointment.status }}
                                            </span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                                            ${{ typeof appointment.amount === 'number' ? appointment.amount.toFixed(2) : parseFloat(appointment.amount || appointment.service?.price || appointment.price || 0).toFixed(2) }}
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                            <div class="flex flex-wrap gap-2">
                                                <button
                                                    @click="viewAppointment(appointment)"
                                                    class="text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300 transition-colors duration-200"
                                                    title="View appointment details"
                                                >
                                                    <i class="fas fa-eye mr-1"></i>
                                                    View
                                                </button>

                                                <!-- Video Call Buttons for Telemedicine - Role-based -->
                                                <template v-if="appointment.is_telemedicine && ['scheduled', 'confirmed', 'in_progress'].includes(appointment.status)">
                                                    <!-- Start Call Button - Only for Providers -->
                                                    <button
                                                        v-if="canStartVideoCall(appointment)"
                                                        @click="startVideoCall(appointment)"
                                                        class="text-purple-600 hover:text-purple-900 dark:text-purple-400 dark:hover:text-purple-300 transition-colors duration-200"
                                                        title="Start video call"
                                                    >
                                                        <i class="fas fa-video mr-1"></i>
                                                        Start Call
                                                    </button>

                                                    <!-- Join Call Button - Only for Patients when session exists -->
                                                    <button
                                                        v-if="canJoinVideoCall(appointment)"
                                                        @click="joinVideoCall(appointment)"
                                                        class="text-green-600 hover:text-green-900 dark:text-green-400 dark:hover:text-green-300 transition-colors duration-200"
                                                        title="Join video call"
                                                    >
                                                        <i class="fas fa-phone mr-1"></i>
                                                        Join Call
                                                    </button>

                                                    <!-- Join Consult Button for Patients -->
                                                    <button
                                                        v-if="getUserRoleForAppointment(appointment) === 'patient' && !appointment.video_session_id"
                                                        @click="joinConsult(appointment)"
                                                        :disabled="joinConsultLoading[appointment.id]"
                                                        class="inline-flex items-center px-4 py-2.5 bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white text-sm font-medium rounded-lg transition-all duration-200 shadow-sm hover:shadow-md disabled:opacity-50 disabled:cursor-not-allowed"
                                                        title="Join consultation"
                                                    >
                                                        <i v-if="joinConsultLoading[appointment.id]" class="fas fa-spinner fa-spin mr-2"></i>
                                                        <i v-else class="fas fa-video mr-2"></i>
                                                        {{ joinConsultLoading[appointment.id] ? 'Joining...' : 'Join Consult' }}
                                                    </button>
                                                </template>

                                                <button
                                                    v-if="appointment.status === 'scheduled'"
                                                    @click="editAppointment(appointment)"
                                                    class="text-green-600 hover:text-green-900 dark:text-green-400 dark:hover:text-green-300 transition-colors duration-200"
                                                    title="Edit appointment"
                                                >
                                                    <i class="fas fa-edit mr-1"></i>
                                                    Edit
                                                </button>
                                                <button
                                                    v-if="appointment.status === 'scheduled'"
                                                    @click="cancelAppointment(appointment)"
                                                    :disabled="isActionLoading(appointment.id)"
                                                    class="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300 transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                                                    title="Cancel appointment"
                                                >
                                                    <i v-if="isActionLoading(appointment.id)" class="fas fa-spinner fa-spin mr-1"></i>
                                                    <i v-else class="fas fa-times mr-1"></i>
                                                    {{ isActionLoading(appointment.id) ? 'Cancelling...' : 'Cancel' }}
                                                </button>

                                                <button
                                                    v-if="['completed', 'cancelled'].includes(appointment.status)"
                                                    @click="deleteAppointment(appointment)"
                                                    :disabled="isActionLoading(appointment.id)"
                                                    class="text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-300 transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                                                    title="Delete appointment"
                                                >
                                                    <i v-if="isActionLoading(appointment.id)" class="fas fa-spinner fa-spin mr-1"></i>
                                                    <i v-else class="fas fa-trash mr-1"></i>
                                                    {{ isActionLoading(appointment.id) ? 'Deleting...' : 'Delete' }}
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Video Call Modal -->
        <VideoCallModal
            v-if="selectedAppointment"
            :is-open="showVideoCall"
            :appointment-id="selectedAppointment.id"
            :appointment="selectedAppointment"
            :user-role="userRole"
            @close="closeVideoCall"
            @error="handleVideoCallError"
        />

        <!-- Reason Popup Modal -->
        <div v-if="showReasonPopup" class="fixed inset-0 z-50 overflow-y-auto" aria-labelledby="modal-title" role="dialog" aria-modal="true">
            <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
                <!-- Background overlay with blur effect -->
                <div class="fixed inset-0 bg-white bg-opacity-20 backdrop-blur-sm transition-opacity" aria-hidden="true" @click="closeReasonPopup"></div>

                <!-- Modal panel -->
                <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
                    <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                        <div class="sm:flex sm:items-start">
                            <div class="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-teal-100 sm:mx-0 sm:h-10 sm:w-10">
                                <svg class="h-6 w-6 text-teal-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                </svg>
                            </div>
                            <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left w-full">
                                <h3 class="text-lg leading-6 font-medium text-gray-900" id="modal-title">
                                    Reason for Visit
                                </h3>
                                <div class="mt-3">
                                    <p class="text-sm text-gray-700 leading-relaxed whitespace-pre-wrap">
                                        {{ formatReason(selectedReason) }}
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                        <button
                            type="button"
                            @click="closeReasonPopup"
                            class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-teal-600 text-base font-medium text-white hover:bg-teal-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-teal-500 sm:ml-3 sm:w-auto sm:text-sm"
                        >
                            Close
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </AppLayout>
</template>
