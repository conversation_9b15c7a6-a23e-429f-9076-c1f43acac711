<script setup>
import AppLayout from '@/layouts/AppLayout.vue';
import { Head, Link, router } from '@inertiajs/vue3';
import { ref, reactive, computed } from 'vue';

const props = defineProps({
    appointment: {
        type: Object,
        required: true
    }
});

// Dynamic breadcrumbs based on user role
const breadcrumbs = computed(() => {
    // Get user from props.appointment or use a default
    const userRole = props.appointment?.patient?.user?.role || props.appointment?.provider?.user?.role || 'patient';
    let appointmentsHref = '/appointments';

    if (userRole === 'provider') {
        appointmentsHref = '/provider/appointments';
    } else if (userRole === 'patient') {
        appointmentsHref = '/patient/appointments';
    } else if (userRole === 'admin' || userRole === 'manager') {
        appointmentsHref = '/manage/appointments';
    }

    return [
        { title: 'Dashboard', href: '/dashboard' },
        { title: 'Appointments', href: appointmentsHref },
        { title: 'Edit Appointment', href: '#' },
    ];
});

const loading = ref(false);
const form = reactive({
    date: props.appointment.date || props.appointment.scheduled_at?.split('T')[0] || '',
    time: props.appointment.time || props.appointment.scheduled_at?.split('T')[1]?.substring(0,5) || '',
    notes: props.appointment.notes || '',
    type: props.appointment.is_telemedicine ? 'telemedicine' : 'in-person'
});

const errors = ref({});

const updateAppointment = async () => {
    loading.value = true;
    errors.value = {};

    try {
        // Get CSRF token from meta tag
        const token = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');

        const response = await fetch(`/save-appointment/${props.appointment.id}`, {
            method: 'PUT',
            headers: {
                'Accept': 'application/json',
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest',
                'X-CSRF-TOKEN': token || '',
            },
            credentials: 'same-origin',
            body: JSON.stringify({
                date: form.date,
                time_slot: {
                    start_time: form.time,
                    end_time: form.time // This should be calculated based on duration
                },
                notes: form.notes,
                is_telemedicine: form.type === 'telemedicine'
            })
        });

        const data = await response.json().catch(() => ({}));

        if (response.ok) {
            alert('Appointment updated successfully.');
            router.visit(`/appointments/${props.appointment.id}`);
        } else {
            if (data.errors) {
                errors.value = data.errors;
            } else {
                alert(data.message || 'Failed to update appointment. Please try again.');
            }
        }
    } catch (error) {
        console.error('Error updating appointment:', error);
        alert('Failed to update appointment. Please try again.');
    } finally {
        loading.value = false;
    }
};

const cancel = () => {
    router.visit(`/appointments/${props.appointment.id}`);
};

const formatDate = (date) => {
    return new Date(date).toISOString().split('T')[0];
};

const formatTime = (time) => {
    if (!time) return '';
    // Handle different time formats
    if (time.includes(':')) {
        return time.substring(0, 5); // HH:MM format
    }
    return time;
};


</script>

<template>
    <Head title="Edit Appointment" />

    <AppLayout>
        <template #header>
            <div class="flex items-center justify-between">
                <div>
                    <h2 class="text-xl font-semibold leading-tight text-gray-800 dark:text-gray-200">
                        Edit Appointment
                    </h2>
                    <nav class="flex mt-2" aria-label="Breadcrumb">
                        <ol class="inline-flex items-center space-x-1 md:space-x-3">
                            <li v-for="(breadcrumb, index) in breadcrumbs" :key="index" class="inline-flex items-center">
                                <Link v-if="index < breadcrumbs.length - 1" 
                                    :href="breadcrumb.href" 
                                    class="text-sm font-medium text-gray-500 hover:text-blue-600 dark:text-gray-400 dark:hover:text-white">
                                    {{ breadcrumb.title }}
                                </Link>
                                <span v-else class="text-sm font-medium text-gray-700 dark:text-gray-400">
                                    {{ breadcrumb.title }}
                                </span>
                                <svg v-if="index < breadcrumbs.length - 1" class="w-3 h-3 mx-1 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                                </svg>
                            </li>
                        </ol>
                    </nav>
                </div>
            </div>
        </template>

        <div class="py-12">
            <div class="mx-auto max-w-4xl sm:px-6 lg:px-8">
                <!-- Edit Form -->
                <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <form @submit.prevent="updateAppointment">
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <!-- Left Column -->
                                <div>
                                    <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">
                                        Appointment Details
                                    </h3>
                                    
                                    <div class="space-y-4">
                                        <div>
                                            <label for="date" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                                                Date *
                                            </label>
                                            <input
                                                id="date"
                                                v-model="form.date"
                                                type="date"
                                                :min="new Date().toISOString().split('T')[0]"
                                                class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                                                required
                                            />
                                            <p v-if="errors.date" class="mt-1 text-sm text-red-600">{{ errors.date[0] }}</p>
                                        </div>
                                        
                                        <div>
                                            <label for="time" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                                                Time *
                                            </label>
                                            <input
                                                id="time"
                                                v-model="form.time"
                                                type="time"
                                                class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                                                required
                                            />
                                            <p v-if="errors.time_slot" class="mt-1 text-sm text-red-600">{{ errors.time_slot[0] }}</p>
                                        </div>
                                        
                                        <div>
                                            <label for="type" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                                                Type *
                                            </label>
                                            <select
                                                id="type"
                                                v-model="form.type"
                                                class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                                                required
                                            >
                                                <option value="telemedicine">Telemedicine</option>
                                                <option value="in-person">In-Person</option>
                                            </select>
                                            <p v-if="errors.is_telemedicine" class="mt-1 text-sm text-red-600">{{ errors.is_telemedicine[0] }}</p>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- Right Column -->
                                <div>
                                    <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">
                                        Additional Information
                                    </h3>
                                    
                                    <div class="space-y-4">
                                        <div>
                                            <label class="block text-sm font-medium text-gray-500 dark:text-gray-400">Service</label>
                                            <p class="mt-1 text-sm text-gray-900 dark:text-gray-100">{{ appointment.service || appointment.reason || 'Consultation' }}</p>
                                        </div>

                                        <div>
                                            <label class="block text-sm font-medium text-gray-500 dark:text-gray-400">Provider</label>
                                            <p class="mt-1 text-sm text-gray-900 dark:text-gray-100">{{ appointment.provider_name || appointment.provider?.name || 'N/A' }}</p>
                                        </div>

                                        <div>
                                            <label class="block text-sm font-medium text-gray-500 dark:text-gray-400">Amount</label>
                                            <p class="mt-1 text-sm text-gray-900 dark:text-gray-100">${{ typeof appointment.amount === 'number' ? appointment.amount.toFixed(2) : parseFloat(appointment.amount || 0).toFixed(2) }}</p>
                                        </div>
                                        
                                        <div>
                                            <label for="notes" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                                                Notes
                                            </label>
                                            <textarea
                                                id="notes"
                                                v-model="form.notes"
                                                rows="4"
                                                class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                                                placeholder="Add any additional notes..."
                                            ></textarea>
                                            <p v-if="errors.notes" class="mt-1 text-sm text-red-600">{{ errors.notes[0] }}</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Form Actions -->
                            <div class="mt-6 flex justify-end space-x-3">
                                <button
                                    type="button"
                                    @click="cancel"
                                    class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded transition-colors duration-200"
                                >
                                    Cancel
                                </button>
                                <button
                                    type="submit"
                                    :disabled="loading"
                                    class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                                >
                                    <i v-if="loading" class="fas fa-spinner fa-spin mr-2"></i>
                                    <i v-else class="fas fa-save mr-2"></i>
                                    {{ loading ? 'Updating...' : 'Update Appointment' }}
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </AppLayout>
</template>
