const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/Index-C7Egf7sZ.js","assets/vendor-CKE3WRFf.js","assets/AppLayout.vue_vue_type_script_setup_true_lang-vKoR9GS2.js","assets/_plugin-vue_export-helper-DlAUqK2U.js","assets/Primitive-B8nQAqQg.js","assets/createLucideIcon-Dy3o-9bT.js","assets/AppLayout-B4Zax8Ug.css","assets/Show-Df_UkxKn.js","assets/Create-C6m4Gcl2.js","assets/Index-DFBrdY1N.js","assets/BulkImportModal-DI1Em9AY.js","assets/AppointmentDetail-B_ooxZ41.js","assets/AppointmentEdit-CUeNMPxz.js","assets/AppointmentPayment-B97Zcic9.js","assets/Appointments-Dgx_K5xO.js","assets/Appointments-Dc5S3XBs.css","assets/Chat-DKogZaIq.js","assets/ChatInput-DxvfcMrj.js","assets/ChatInput-BmGnubPc.css","assets/Chat-DtNjTBwp.css","assets/ChatHistory-BgsobRXo.js","assets/Chats-D4OyZTUN.js","assets/Clinics-gQRAzmgM.js","assets/Clubs-Cwx_tJsa.js","assets/CreditHistory-Dx_mwwfO.js","assets/Credits-DbmNE8I_.js","assets/Dashboard-BGllZaMi.js","assets/Dashboard_backup-qRNL5HEF.js","assets/Dashboard_backup-CW3kC41H.css","assets/Discover-DZdAQ7IB.js","assets/EmailTemplates-CABPCmjt.js","assets/Notifications-C3kwQnWx.js","assets/Patients-BfrHTuw2.js","assets/Payments-CxMKOT7B.js","assets/Permissions-CiBWeNot.js","assets/Availability-BC9clz5R.js","assets/Earnings-D7ipHsx9.js","assets/Patients-BzYcrwWB.js","assets/Products-CAz5-hMi.js","assets/Profile-BPlIqqGI.js","assets/Schedule-Dwo1kyYj.js","assets/Services-DFVR2nak.js","assets/ProviderRegister-DIRo_C9w.js","assets/InputError.vue_vue_type_script_setup_true_lang-D2QV6psC.js","assets/Providers-DZVRrTsd.js","assets/Referrals-CCnwg71B.js","assets/Services-CMpDCw4Z.js","assets/Shop-Ddt7807A.js","assets/Cart-CgKlkiWL.js","assets/Checkout-BhZWO-pv.js","assets/OrderDetail-BoZGuQrV.js","assets/Orders-BtOmZ8Ok.js","assets/ProductDetail-CIdDKwNW.js","assets/SystemVerification-L1JvsEIF.js","assets/Users-DR87yG02.js","assets/Waitlist-BF-FpiuG.js","assets/Welcome-BECHGD7Z.js","assets/Welcome-DrPWS9zi.css","assets/ConfirmPassword-nL1qk42P.js","assets/index-CviDBDnS.js","assets/Label.vue_vue_type_script_setup_true_lang-CcY9peuA.js","assets/index-BzpNoPfh.js","assets/AuthLayout.vue_vue_type_script_setup_true_lang-BUt3QzCv.js","assets/ForgotPassword-BWMKo8rZ.js","assets/TextLink.vue_vue_type_script_setup_true_lang-Craz9_nS.js","assets/FounderSignup-DgayLd9K.js","assets/Register-Cb6VAihW.js","assets/Register-B2WbpIMy.css","assets/ResetPassword-cjrXsJOH.js","assets/VerifyEmail-CmuJOrAz.js","assets/Appearance-CPpDWvc_.js","assets/HeadingSmall.vue_vue_type_script_setup_true_lang-D8M_Uo37.js","assets/Layout.vue_vue_type_script_setup_true_lang-CSboWTQp.js","assets/Appearance-CB0SEYXv.css","assets/AppointmentPreferences-DRfyR4_s.js","assets/useBodyScrollLock-YawL5dFy.js","assets/Password-DO3BAL_m.js","assets/Profile-K9cG_DV5.js"])))=>i.map(i=>d[i]);
import{r as T,o as I,c as E,w as S,a as v,L as y,W as D,b as w,k as V,h as k}from"./vendor-CKE3WRFf.js";const C="modulepreload",x=function(e){return"/build/"+e},A={},t=function(r,o,u){let d=Promise.resolve();if(o&&o.length>0){document.getElementsByTagName("link");const s=document.querySelector("meta[property=csp-nonce]"),_=(s==null?void 0:s.nonce)||(s==null?void 0:s.getAttribute("nonce"));d=Promise.allSettled(o.map(n=>{if(n=x(n),n in A)return;A[n]=!0;const l=n.endsWith(".css"),i=l?'[rel="stylesheet"]':"";if(document.querySelector(`link[href="${n}"]${i}`))return;const a=document.createElement("link");if(a.rel=l?"stylesheet":C,l||(a.as="script"),a.crossOrigin="",a.href=n,_&&a.setAttribute("nonce",_),document.head.appendChild(a),l)return new Promise((f,O)=>{a.addEventListener("load",f),a.addEventListener("error",()=>O(new Error(`Unable to preload CSS for ${n}`)))})}))}function c(s){const _=new Event("vite:preloadError",{cancelable:!0});if(_.payload=s,window.dispatchEvent(_),!_.defaultPrevented)throw s}return d.then(s=>{for(const _ of s||[])_.status==="rejected"&&c(_.reason);return r().catch(c)})};async function F(e,r){for(const o of Array.isArray(e)?e:[e]){const u=r[o];if(!(typeof u>"u"))return typeof u=="function"?u():u}throw new Error(`Page not found: ${e}`)}function P(e){if(!(typeof window>"u"))if(e==="system"){const o=window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light";document.documentElement.classList.toggle("dark",o==="dark")}else document.documentElement.classList.toggle("dark",e==="dark")}const b=(e,r,o=365)=>{if(typeof document>"u")return;const u=o*24*60*60;document.cookie=`${e}=${r};path=/;max-age=${u};SameSite=Lax`},z=()=>typeof window>"u"?null:window.matchMedia("(prefers-color-scheme: dark)"),L=()=>typeof window>"u"?null:localStorage.getItem("appearance"),$=()=>{const e=L();P(e||"system")};function j(){var r;if(typeof window>"u")return;const e=L();P(e||"system"),(r=z())==null||r.addEventListener("change",$)}function Q(){const e=T("system");I(()=>{const o=localStorage.getItem("appearance");o&&(e.value=o)});function r(o){e.value=o,localStorage.setItem("appearance",o),b("appearance",o),P(o)}return{appearance:e,updateAppearance:r}}const m={small:{name:"Small",scale:.875,description:"Smaller text for better screen space"},normal:{name:"Normal",scale:1,description:"Default text size"},large:{name:"Large",scale:1.125,description:"Larger text for better readability"},xlarge:{name:"Extra Large",scale:1.25,description:"Extra large text for accessibility"}},p=T("normal"),N=()=>{const e=localStorage.getItem("medroid-font-size");e&&m[e]&&(p.value=e)},q=e=>{localStorage.setItem("medroid-font-size",e)};N();function W(){const e=E(()=>{var i;return((i=m[p.value])==null?void 0:i.scale)||1}),r=E(()=>{var i;return((i=m[p.value])==null?void 0:i.name)||"Normal"}),o=E(()=>{var i;return((i=m[p.value])==null?void 0:i.description)||""}),u=E(()=>Object.entries(m).map(([i,a])=>({key:i,...a}))),d=E(()=>({"--font-scale":e.value,"--text-xs":`${.75*e.value}rem`,"--text-sm":`${.875*e.value}rem`,"--text-base":`${1*e.value}rem`,"--text-lg":`${1.125*e.value}rem`,"--text-xl":`${1.25*e.value}rem`,"--text-2xl":`${1.5*e.value}rem`,"--text-3xl":`${1.875*e.value}rem`,"--text-4xl":`${2.25*e.value}rem`,"--text-5xl":`${3*e.value}rem`,"--text-6xl":`${3.75*e.value}rem`})),c=i=>{m[i]&&(p.value=i,q(i),l())},s=()=>{const i=Object.keys(m),a=i.indexOf(p.value);a<i.length-1&&c(i[a+1])},_=()=>{const i=Object.keys(m),a=i.indexOf(p.value);a>0&&c(i[a-1])},n=()=>{c("normal")},l=()=>{const i=document.documentElement;Object.entries(d.value).forEach(([a,f])=>{i.style.setProperty(a,f)}),document.body.className=document.body.className.replace(/font-size-\w+/g,""),document.body.classList.add(`font-size-${p.value}`)};return S(p,()=>{l()},{immediate:!0}),{currentFontSize:p,fontSizeScale:e,fontSizeName:r,fontSizeDescription:o,availableFontSizes:u,fontSizeStyles:d,setFontSize:c,increaseFontSize:s,decreaseFontSize:_,resetFontSize:n,applyFontSizeToDocument:l,FONT_SIZES:m}}const X="Medroid";v.defaults.headers.common["X-Requested-With"]="XMLHttpRequest";v.defaults.withCredentials=!0;const g=()=>{const e=document.head.querySelector('meta[name="csrf-token"]');return e?e.content:null},h=async()=>{try{if((await fetch("/sanctum/csrf-cookie",{method:"GET",credentials:"same-origin",headers:{Accept:"application/json"}})).ok)return await new Promise(r=>setTimeout(r,100)),g()}catch(e){console.error("Failed to refresh CSRF token:",e)}return null},R=g();R&&(v.defaults.headers.common["X-CSRF-TOKEN"]=R);const U=async()=>{let e=g();return e||(console.log("Initializing CSRF token..."),e=await h(),e?console.log("CSRF token initialized successfully"):console.warn("Failed to initialize CSRF token")),e};v.interceptors.request.use(async e=>{let r=g();return r||(console.warn("No CSRF token found, attempting to refresh..."),r=await h()),r?e.headers["X-CSRF-TOKEN"]=r:console.error("Unable to obtain CSRF token"),e.headers.Accept=e.headers.Accept||"application/json",e.headers["Content-Type"]=e.headers["Content-Type"]||"application/json",e},e=>Promise.reject(e));v.interceptors.response.use(e=>e,async e=>{var o,u,d,c,s,_;const r=e.config;if(((o=e.response)==null?void 0:o.status)===419&&!r._retry){console.warn("CSRF token mismatch detected, attempting to refresh and retry..."),r._retry=!0;try{const n=await h();if(n)return r.headers["X-CSRF-TOKEN"]=n,v.defaults.headers.common["X-CSRF-TOKEN"]=n,await new Promise(l=>setTimeout(l,50)),v(r);console.error("Failed to refresh CSRF token, reloading page..."),setTimeout(()=>{window.confirm("Session expired. Reload page to continue?")&&window.location.reload()},1e3)}catch(n){console.error("Error during token refresh:",n)}}if(((u=e.response)==null?void 0:u.status)===500&&!r._serverRetry&&!((d=r.url)!=null&&d.includes("/logout"))){console.warn("Server error detected, attempting retry..."),r._serverRetry=!0,await new Promise(n=>setTimeout(n,1e3));try{return v(r)}catch(n){console.error("Server error retry failed:",n)}}return(c=r.url)!=null&&c.includes("/logout")?(console.log("Logout request - not intercepting"),Promise.reject(e)):(((s=e.response)==null?void 0:s.status)===401&&((_=r.url)!=null&&_.includes("/logout")||(console.warn("Authentication failed, redirecting to login..."),window.location.href="/login")),Promise.reject(e))});window.axios=v;y({title:e=>`${e} - ${X}`,resolve:e=>F(`./pages/${e}.vue`,Object.assign({"./pages/Admin/Orders/Index.vue":()=>t(()=>import("./Index-C7Egf7sZ.js"),__vite__mapDeps([0,1,2,3,4,5,6])),"./pages/Admin/Orders/Show.vue":()=>t(()=>import("./Show-Df_UkxKn.js"),__vite__mapDeps([7,2,1,3,4,5,6])),"./pages/Admin/Products/Create.vue":()=>t(()=>import("./Create-C6m4Gcl2.js"),__vite__mapDeps([8,1,2,3,4,5,6])),"./pages/Admin/Products/Index.vue":()=>t(()=>import("./Index-DFBrdY1N.js"),__vite__mapDeps([9,1,2,3,4,5,6,10])),"./pages/AppointmentDetail.vue":()=>t(()=>import("./AppointmentDetail-B_ooxZ41.js"),__vite__mapDeps([11,2,1,3,4,5,6])),"./pages/AppointmentEdit.vue":()=>t(()=>import("./AppointmentEdit-CUeNMPxz.js"),__vite__mapDeps([12,1,2,3,4,5,6])),"./pages/AppointmentPayment.vue":()=>t(()=>import("./AppointmentPayment-B97Zcic9.js"),__vite__mapDeps([13,1,2,3,4,5,6])),"./pages/Appointments.vue":()=>t(()=>import("./Appointments-Dgx_K5xO.js"),__vite__mapDeps([14,2,1,3,4,5,6,15])),"./pages/Chat.vue":()=>t(()=>import("./Chat-DKogZaIq.js"),__vite__mapDeps([16,1,2,3,4,5,6,17,18,19])),"./pages/ChatHistory.vue":()=>t(()=>import("./ChatHistory-BgsobRXo.js"),__vite__mapDeps([20,2,1,3,4,5,6])),"./pages/Chats.vue":()=>t(()=>import("./Chats-D4OyZTUN.js"),__vite__mapDeps([21,1,2,3,4,5,6])),"./pages/Clinics.vue":()=>t(()=>import("./Clinics-gQRAzmgM.js"),__vite__mapDeps([22,1,2,3,4,5,6])),"./pages/Clubs.vue":()=>t(()=>import("./Clubs-Cwx_tJsa.js"),__vite__mapDeps([23,1,2,3,4,5,6])),"./pages/CreditHistory.vue":()=>t(()=>import("./CreditHistory-Dx_mwwfO.js"),__vite__mapDeps([24,1,2,3,4,5,6])),"./pages/Credits.vue":()=>t(()=>import("./Credits-DbmNE8I_.js"),__vite__mapDeps([25,1,2,3,4,5,6])),"./pages/Dashboard.vue":()=>t(()=>import("./Dashboard-BGllZaMi.js"),__vite__mapDeps([26,1,2,3,4,5,6])),"./pages/Dashboard_backup.vue":()=>t(()=>import("./Dashboard_backup-qRNL5HEF.js"),__vite__mapDeps([27,1,2,3,4,5,6,28])),"./pages/Discover.vue":()=>t(()=>import("./Discover-DZdAQ7IB.js"),__vite__mapDeps([29,2,1,3,4,5,6])),"./pages/EmailTemplates.vue":()=>t(()=>import("./EmailTemplates-CABPCmjt.js"),__vite__mapDeps([30,1,2,3,4,5,6])),"./pages/Notifications.vue":()=>t(()=>import("./Notifications-C3kwQnWx.js"),__vite__mapDeps([31,2,1,3,4,5,6])),"./pages/Patients.vue":()=>t(()=>import("./Patients-BfrHTuw2.js"),__vite__mapDeps([32,1,2,3,4,5,6])),"./pages/Payments.vue":()=>t(()=>import("./Payments-CxMKOT7B.js"),__vite__mapDeps([33,1,2,3,4,5,6])),"./pages/Permissions.vue":()=>t(()=>import("./Permissions-CiBWeNot.js"),__vite__mapDeps([34,2,1,3,4,5,6])),"./pages/Provider/Availability.vue":()=>t(()=>import("./Availability-BC9clz5R.js"),__vite__mapDeps([35,1,2,3,4,5,6])),"./pages/Provider/Earnings.vue":()=>t(()=>import("./Earnings-D7ipHsx9.js"),__vite__mapDeps([36,2,1,3,4,5,6])),"./pages/Provider/Patients.vue":()=>t(()=>import("./Patients-BzYcrwWB.js"),__vite__mapDeps([37,1,2,3,4,5,6])),"./pages/Provider/Products.vue":()=>t(()=>import("./Products-CAz5-hMi.js"),__vite__mapDeps([38,1,2,3,4,5,6,10])),"./pages/Provider/Profile.vue":()=>t(()=>import("./Profile-BPlIqqGI.js"),__vite__mapDeps([39,1,2,3,4,5,6])),"./pages/Provider/Schedule.vue":()=>t(()=>import("./Schedule-Dwo1kyYj.js"),__vite__mapDeps([40,1,2,3,4,5,6])),"./pages/Provider/Services.vue":()=>t(()=>import("./Services-DFVR2nak.js"),__vite__mapDeps([41,2,1,3,4,5,6])),"./pages/ProviderRegister.vue":()=>t(()=>import("./ProviderRegister-DIRo_C9w.js"),__vite__mapDeps([42,1,43])),"./pages/Providers.vue":()=>t(()=>import("./Providers-DZVRrTsd.js"),__vite__mapDeps([44,1,2,3,4,5,6])),"./pages/Referrals.vue":()=>t(()=>import("./Referrals-CCnwg71B.js"),__vite__mapDeps([45,2,1,3,4,5,6])),"./pages/Services.vue":()=>t(()=>import("./Services-CMpDCw4Z.js"),__vite__mapDeps([46,1,2,3,4,5,6])),"./pages/Shop.vue":()=>t(()=>import("./Shop-Ddt7807A.js"),__vite__mapDeps([47,1,2,3,4,5,6])),"./pages/Shop/Cart.vue":()=>t(()=>import("./Cart-CgKlkiWL.js"),__vite__mapDeps([48,2,1,3,4,5,6])),"./pages/Shop/Checkout.vue":()=>t(()=>import("./Checkout-BhZWO-pv.js"),__vite__mapDeps([49,1,2,3,4,5,6])),"./pages/Shop/OrderDetail.vue":()=>t(()=>import("./OrderDetail-BoZGuQrV.js"),__vite__mapDeps([50,2,1,3,4,5,6])),"./pages/Shop/Orders.vue":()=>t(()=>import("./Orders-BtOmZ8Ok.js"),__vite__mapDeps([51,1,2,3,4,5,6])),"./pages/Shop/ProductDetail.vue":()=>t(()=>import("./ProductDetail-CIdDKwNW.js"),__vite__mapDeps([52,2,1,3,4,5,6])),"./pages/SystemVerification.vue":()=>t(()=>import("./SystemVerification-L1JvsEIF.js"),__vite__mapDeps([53,1,2,3,4,5,6])),"./pages/Users.vue":()=>t(()=>import("./Users-DR87yG02.js"),__vite__mapDeps([54,1,2,3,4,5,6])),"./pages/Waitlist.vue":()=>t(()=>import("./Waitlist-BF-FpiuG.js"),__vite__mapDeps([55,1,2,3,4,5,6])),"./pages/Welcome.vue":()=>t(()=>import("./Welcome-BECHGD7Z.js"),__vite__mapDeps([56,1,17,3,18,57])),"./pages/auth/ConfirmPassword.vue":()=>t(()=>import("./ConfirmPassword-nL1qk42P.js"),__vite__mapDeps([58,1,43,59,4,60,61,62,5])),"./pages/auth/ForgotPassword.vue":()=>t(()=>import("./ForgotPassword-BWMKo8rZ.js"),__vite__mapDeps([63,1,43,64,59,4,60,61,62,5])),"./pages/auth/FounderSignup.vue":()=>t(()=>import("./FounderSignup-DgayLd9K.js"),__vite__mapDeps([65,1,43])),"./pages/auth/Register.vue":()=>t(()=>import("./Register-Cb6VAihW.js"),__vite__mapDeps([66,1,43,3,67])),"./pages/auth/ResetPassword.vue":()=>t(()=>import("./ResetPassword-cjrXsJOH.js"),__vite__mapDeps([68,1,43,59,4,60,61,62,5])),"./pages/auth/VerifyEmail.vue":()=>t(()=>import("./VerifyEmail-CmuJOrAz.js"),__vite__mapDeps([69,1,64,59,4,62,5])),"./pages/settings/Appearance.vue":()=>t(()=>import("./Appearance-CPpDWvc_.js"),__vite__mapDeps([70,1,3,5,71,2,4,6,72,59,61,73])),"./pages/settings/AppointmentPreferences.vue":()=>t(()=>import("./AppointmentPreferences-DRfyR4_s.js"),__vite__mapDeps([74,1,72,59,4,61,60,75])),"./pages/settings/Password.vue":()=>t(()=>import("./Password-DO3BAL_m.js"),__vite__mapDeps([76,1,43,2,3,4,5,6,72,59,61,71,60])),"./pages/settings/Profile.vue":()=>t(()=>import("./Profile-K9cG_DV5.js"),__vite__mapDeps([77,1,71,43,59,4,75,61,60,5,2,3,6,72]))})),setup({el:e,App:r,props:o,plugin:u}){w({render:()=>k(r,o)}).use(u).use(V).mount(e)},progress:{color:"#4B5563"}}).then(()=>{D.on("error",e=>{var r,o,u,d,c,s;if(console.log("Inertia request error:",e),(o=(r=e.response)==null?void 0:r.url)!=null&&o.includes("/logout")||(c=(d=(u=e.response)==null?void 0:u.config)==null?void 0:d.url)!=null&&c.includes("/logout")){console.log("Logout error handled gracefully, redirecting to home"),window.location.href="/";return}if(((s=e.response)==null?void 0:s.status)===419){console.warn("CSRF error on Inertia request, reloading page"),window.location.reload();return}})}).catch(e=>{console.error("Error initializing Inertia app:",e)});j();const{applyFontSizeToDocument:K}=W();K();"serviceWorker"in navigator&&navigator.serviceWorker.getRegistrations().then(function(e){for(const r of e)(r.scope.includes("datadog")||r.scope.includes("sw.js"))&&r.unregister()}).catch(function(e){});U().catch(e=>{console.warn("Failed to initialize CSRF token on startup:",e)});export{Q as a,W as u};
